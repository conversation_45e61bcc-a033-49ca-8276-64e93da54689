<?xml version="1.0"?>
<!--
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="banners_listing_data_source" xsi:type="string">MadHat\Banners\Model\ResourceModel\Banners\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <type name="MadHat\Banners\Model\ResourceModel\Banners\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">MadHat_banners</argument>
            <argument name="eventPrefix" xsi:type="string">banners_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">banners_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">MadHat\Banners\Model\ResourceModel\Banners</argument>
        </arguments>
    </type>

    <type name="MadHat\Banners\Controller\Adminhtml\Banners\Image\Upload">
        <arguments>
            <argument name="imageUploader" xsi:type="object">BannerImageUpload</argument>
        </arguments>
    </type>

    <virtualType name="BannerImageUpload" type="MadHat\Banners\Model\ImageUploader">
        <arguments>
            <argument name="baseTmpPath" xsi:type="string">banners</argument>
            <argument name="basePath" xsi:type="string">banners</argument>
            <argument name="allowedExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="bannersGridFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="bannersGridDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">MadHat\Banners\Model\ResourceModel\Banners\Grid\Collection</argument>
            <argument name="filterPool" xsi:type="object" shared="false">bannersGridFilterPool</argument>
        </arguments>
    </virtualType>

</config>