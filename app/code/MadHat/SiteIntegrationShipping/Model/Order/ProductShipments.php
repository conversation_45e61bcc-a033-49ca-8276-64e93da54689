<?php

namespace MadHat\SiteIntegrationShipping\Model\Order;

use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\SiteIntegrationCore\Model\Common;
use MadHat\SiteIntegrationShipping\Api\Data\ShippingInterfaceFactory;
use MadHat\SiteIntegrationShipping\Api\Data\ShippingInterface;
use MadHat\SiteIntegrationShipping\Api\ShippingRepositoryInterface;
use MadHat\SiteIntegrationMapping\Api\MappingRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\DB\TransactionFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\ShipmentCommentInterfaceFactory;
use Magento\Sales\Api\Data\ShipmentTrackInterfaceFactory;
use Magento\Sales\Api\ShipmentRepositoryInterface;
use Magento\Sales\Model\Convert\OrderFactory;
use Magento\Sales\Model\Order\Shipment\Sender\EmailSender;
use Psr\Log\LoggerInterface;

class ProductShipments
{
    const ARR_KEY_ORDERSTATUSDATA = 'OrderStatusData';
    const ARR_KEY_CONSIGNMENTS = 'Consignments';
    const ARR_KEY_PRODUCTSHIPMENTS = 'ProductShipments';
    const ARR_KEY_PRODUCTNO = 'ProductNo';
    const ARR_KEY_PICKLISTNO = 'PicklistNo';
    const ARR_KEY_DELIVERYGROUP = 'DeliveryGroup';
    const ARR_KEY_QUANTITY = 'Quantity';
    const ARR_KEY_SHIPMENTID = 'ShipmentId';
    const ARR_KEY_SCANNINGTIMESTAMP = 'ScanningTimestamp';
    const ARR_KEY_SHIPMENTMETHOD = 'ShipmentMethod';

    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var ShipmentRepositoryInterface
     */
    private $shipmentRepository;
    /**
     * @var ShippingInterface
     */
    private $ipiccoloShipmentFactory;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    /**
     * @var ShippingRepositoryInterface
     */
    private $ipiccoloShipmentRepository;
    /**
     * @var ShipmentCommentInterfaceFactory
     */
    private $shipmentCommentInterfaceFactory;
    /**
     * @var ShipmentTrackInterfaceFactory
     */
    private $shipmentTrackInterfaceFactory;
    /**
     * @var TransactionFactory
     */
    private $transactionFactory;
    /**
     * @var ShipmentSender
     */
    private $shipmentSender;
    /**
     * @var OrderFactory
     */
    private $convertOrderFactory;

    /**
     * @var \Magento\Sales\Model\Convert\Order
     */
    private $orderConverter;
    /**
     * @var MappingRepositoryInterface
     */
    private $mappingRepository;
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;
    /**
     * @var MadhatOrderInfoRepositoryInterface
     */
    private MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /**
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param ResourceConnection $resourceConnection
     * @param MappingRepositoryInterface $mappingRepository
     * @param EmailSender $shipmentSender
     * @param TransactionFactory $transactionFactory
     * @param ShipmentTrackInterfaceFactory $shipmentTrackInterfaceFactory
     * @param ShipmentCommentInterfaceFactory $shipmentCommentInterfaceFactory
     * @param ShippingRepositoryInterface $ipiccoloShipmentRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param Common $common
     * @param OrderFactory $convertOrderFactory
     * @param ShippingInterfaceFactory $ipiccoloShipmentRepositoryFactory
     * @param ShipmentRepositoryInterface $shipmentRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        MadhatOrderInfoRepositoryInterface                      $madhatOrderInfoRepository,
        ResourceConnection                                      $resourceConnection,
        MappingRepositoryInterface                              $mappingRepository,
        EmailSender                                             $shipmentSender,
        TransactionFactory                                      $transactionFactory,
        \Magento\Sales\Api\Data\ShipmentTrackInterfaceFactory   $shipmentTrackInterfaceFactory,
        \Magento\Sales\Api\Data\ShipmentCommentInterfaceFactory $shipmentCommentInterfaceFactory,
        ShippingRepositoryInterface                             $ipiccoloShipmentRepository,
        SearchCriteriaBuilder                                   $searchCriteriaBuilder,
        Common                                                  $common,
        OrderFactory                                            $convertOrderFactory,
        ShippingInterfaceFactory                                $ipiccoloShipmentRepositoryFactory,
        ShipmentRepositoryInterface                             $shipmentRepository,
        LoggerInterface                                         $logger
    ) {
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->resourceConnection = $resourceConnection;
        $this->mappingRepository = $mappingRepository;
        $this->shipmentSender = $shipmentSender;
        $this->transactionFactory = $transactionFactory;
        $this->shipmentTrackInterfaceFactory = $shipmentTrackInterfaceFactory;
        $this->shipmentCommentInterfaceFactory = $shipmentCommentInterfaceFactory;
        $this->ipiccoloShipmentRepository = $ipiccoloShipmentRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->common = $common;
        $this->convertOrderFactory = $convertOrderFactory;
        $this->ipiccoloShipmentFactory = $ipiccoloShipmentRepositoryFactory;
        $this->shipmentRepository = $shipmentRepository;
        $this->logger = $logger;
    }

    /**
     * @param array $orderStatusData
     * @param OrderInterface $order
     * @return int
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function createShipment(array $orderStatusData, \Magento\Sales\Api\Data\OrderInterface $order): int
    {
        $magentoOrderId = $order->getEntityId();
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$magentoOrderId);
        $fcOrderId = $madhatOrderInfo->getFcOrderId();
        if(!empty($fcOrderId)) { //
            return $this->createShipmentWithoutProductShipments($orderStatusData, $order);
        }
        return $this->createShipmentWithProductShipments($orderStatusData, $order);
    }

    /**
     * Process consignments data from API response
     *
     * @param array $orderStatusData
     * @return array
     */
    private function processingConsignments(array $orderStatusData): array
    {
        $consignments = [];
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $consignments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS];
        }
        return $consignments;
    }

    /**
     * Process PicklistNo from consignments data
     * Returns 0 if PicklistNo doesn't exist
     *
     * @param array $consignments
     * @return int
     */
    private function processingPicklistNo(array $consignments): int
    {
        if (!empty($consignments) && isset($consignments[0][self::ARR_KEY_PICKLISTNO])) {
            return (int)$consignments[0][self::ARR_KEY_PICKLISTNO];
        }
        return 0;
    }

    /**
     * Process ProductShipments data from API response
     * Returns empty array if ProductShipments doesn't exist
     *
     * @param array $orderStatusData
     * @return array
     */
    private function processingProductShipments(array $orderStatusData): array
    {
        $productShipments = [];
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_PRODUCTSHIPMENTS)) {
            $productShipments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_PRODUCTSHIPMENTS];
        }
        return $productShipments;
    }

    /**
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return int Shipment ID on success, 0 on failure
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function createShipmentWithProductShipments($orderStatusData, $order)
    {
        /* check shipment exist for order or not */
        if ($order->canShip()) {
            $deliveryGrouped = $this->getDeliveryGrouped($orderStatusData);
            foreach ($deliveryGrouped as $picklistNo => $itemForShipping) {
                // If Consignments not exist shipping cannot be saved;
                if (!isset($itemForShipping[self::ARR_KEY_CONSIGNMENTS])) {
                    $this->logger->warning(
                        __(
                            '%1 => %2[%3] Order #%4[%5] can not be shipped. Consignments for picklist %6 not exist',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $order->getIncrementId(),
                            $order->getEntityId(),
                            $picklistNo
                        )
                    );
                    continue;
                }
                // Initialize the order shipment object
                $this->orderConverter = $this->convertOrderFactory->create();
                $shipment = $this->orderConverter->toShipment($order);
                if (isset($itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS])) {
                    $productShipments = $itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS];
                    foreach ($productShipments as $item) {
                        // Check if shipping not saved before
                        if ($this->checkProductShipmentsItem($item, $orderStatusData, $order)) {
                            foreach ($order->getAllItems() as $orderItem) { // order items processing
                                // Check if order item has qty to ship or is order is virtual
                                if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                                    continue;
                                }
                                if ($orderItem->getSku() == $item[self::ARR_KEY_PRODUCTNO]) {
                                    $qtyShipped = $item[self::ARR_KEY_QUANTITY];
                                    // Create shipment item with qty
                                    $shipmentItem = $this->orderConverter->itemToShipmentItem($orderItem)->setQty($qtyShipped);
                                    // Add shipment item to shipment
                                    $shipment->addItem($shipmentItem);
                                }
                            }
                        }
                    }
                }
                // add Track numbers
                $consignments = $itemForShipping[self::ARR_KEY_CONSIGNMENTS];
                foreach ($consignments as $consignment) {
                    $shipmentTrack = $this->shipmentTrackInterfaceFactory->create();
                    $shipmentTrack->setTrackNumber($consignment[self::ARR_KEY_SHIPMENTID]);
                    $shipmentTrack->setCarrierCode($this->getCarrierCode($order));
                    $shipmentTrack->setTitle($consignment[self::ARR_KEY_SHIPMENTMETHOD]);
                    $shipment->addTrack($shipmentTrack);
                }
                // add Comment
                $shipmentComment = $this->shipmentCommentInterfaceFactory->create();
                $shipmentComment->setIsVisibleOnFront(0);
                $shipmentComment->setComment(__('PicklistNo: %1', $picklistNo));
                $shipment->addComment($shipmentComment);
                // save Shipping
                // Register shipment
                $shipment->register();
                $shipment->getOrder()->setIsInProcess(true);
                try {
                    $this->transactionFactory->create()->addObject($shipment)->addObject($shipment->getOrder())->save();
                    $shipmentId = (int)$shipment->getId();
                    foreach ($productShipments as $item) {
                        $this->ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId);
                    }
                    return $shipmentId; // Return shipment ID on success
                } catch (\Exception $e) {
                    $this->logger->error(
                        __(
                            '%1 => %2[%3] Magento shipment generate fail. Error: %4',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $e->getMessage()
                        )
                    );
                    return 0; // Return 0 on failure
                }
            }
        } else {
            $this->logger->warning(
                __(
                    '%1 => %2[%3] Order #%4[%5] can not be shipped (canShip = false)',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId(),
                    $order->getEntityId()
                )
            );
        }
        return 0; // Return 0 if shipment cannot be created
    }

    /**
     * Create shipment without product shipments
     *
     * @param $orderStatusData
     * @param $order
     * @return int Shipment ID on success, 0 on failure
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function createShipmentWithoutProductShipments($orderStatusData, $order): int
    {
        if (!$this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $this->logger->debug(
                __(
                    '%1 => %2[%3] %6 not exists for Order #%4[%5]',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId(),
                    $order->getEntityId(),
                    self::ARR_KEY_CONSIGNMENTS
                )
            );
            return 0;
        }
        $this->logger->debug(
            __(
                '%1 => %2[%3] Started shipment creation for Order #%4[%5]',
                __CLASS__,
                __FUNCTION__,
                __LINE__,
                $order->getIncrementId(),
                $order->getEntityId()
            )
        );

        /* check shipment exist for order or not */
        if ($order->canShip()) {
            $this->logger->debug(
                __(
                    '%1 => %2[%3] Order #%4 can be shipped, proceeding',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId()
                )
            );
            // Only one Consigments:
            $picklistNo = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_PICKLISTNO];
            $this->logger->debug(
                __(
                    '%1 => %2[%3] Processing picklist %4',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $picklistNo
                )
            );

            // Initialize the order shipment object
            $this->orderConverter = $this->convertOrderFactory->create();
            $shipment = $this->orderConverter->toShipment($order);

            $itemsAdded = false;
            $itemCount = 0;

            // Add all shippable items regardless of ProductShipments
            foreach ($order->getAllItems() as $orderItem) {
                // Check if order item has qty to ship or is virtual
                if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                    $this->logger->debug(
                        __(
                            '%1 => %2[%3] Skipping item %4 (id: %5): QtyToShip=%6, IsVirtual=%7',
                            __CLASS__,
                            __FUNCTION__,
                            __LINE__,
                            $orderItem->getName(),
                            $orderItem->getItemId(),
                            $orderItem->getQtyToShip(),
                            $orderItem->getIsVirtual() ? 'Yes' : 'No'
                        )
                    );
                    continue;
                }

                $qtyShipped = $orderItem->getQtyToShip();
                // Create shipment item with qty
                $shipmentItem = $this->orderConverter->itemToShipmentItem($orderItem)->setQty($qtyShipped);
                // Add shipment item to shipment
                $shipment->addItem($shipmentItem);
                $itemsAdded = true;
                $itemCount++;

                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Added item %4 (id: %5) to shipment, qty: %6',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $orderItem->getName(),
                        $orderItem->getItemId(),
                        $qtyShipped
                    )
                );
            }

            // Check if any items were added to the shipment
            if (!$itemsAdded) {
                $this->logger->warning(
                    __(
                        '%1 => %2[%3] No items added to shipment for Order #%4[%5], picklist %6. Skipping shipment creation.',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $order->getIncrementId(),
                        $order->getEntityId(),
                        $picklistNo
                    )
                );
                return 0;
            }

            $this->logger->debug(
                __(
                    '%1 => %2[%3] Added %4 items to shipment for Order #%5',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $itemCount,
                    $order->getIncrementId()
                )
            );

            // Only one Consigment
            $trackNumber = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_SHIPMENTID];
            $trackTitle = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS][0][self::ARR_KEY_SHIPMENTMETHOD];


                $shipmentTrack = $this->shipmentTrackInterfaceFactory->create();
                $shipmentTrack->setTrackNumber($trackNumber);
                $shipmentTrack->setCarrierCode($this->getCarrierCode($order));
                $shipmentTrack->setTitle($trackTitle);
                $shipment->addTrack($shipmentTrack);
                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Added tracking %4 with carrier %5 to shipment',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $trackNumber,
                        $trackTitle
                    )
                );
            // add Comment
            $shipmentComment = $this->shipmentCommentInterfaceFactory->create();
            $shipmentComment->setIsVisibleOnFront(0);
            $shipmentComment->setComment(__('PicklistNo: %1', $picklistNo));
            $shipment->addComment($shipmentComment);

            $this->logger->debug(
                __(
                    '%1 => %2[%3] Added comment: PicklistNo: %4',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $picklistNo
                )
            );

            // Register shipment
            $shipment->register();
            $shipment->getOrder()->setIsInProcess(true);

            $this->logger->debug(
                __(
                    '%1 => %2[%3] Shipment registered, order set to in process',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__
                )
            );

            try {
                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Attempting to save shipment',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__
                    )
                );

                $this->transactionFactory->create()->addObject($shipment)->addObject($shipment->getOrder())->save();
                $shipmentId = $shipment->getId();

                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Shipment saved successfully with ID: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $shipmentId
                    )
                );

                // Still update product shipments data if available
                if (isset($itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS])) {
                    $productShipments = $itemForShipping[self::ARR_KEY_PRODUCTSHIPMENTS];
                    foreach ($productShipments as $item) {
                        $this->logger->debug(
                            __(
                                '%1 => %2[%3] Saving product shipment data',
                                __CLASS__,
                                __FUNCTION__,
                                __LINE__
                            )
                        );

                        $this->ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId);
                    }
                }

                return (int)$shipmentId;
            } catch (\Exception $e) {
                $this->logger->error(
                    __(
                        '%1 => %2[%3] Magento shipment generate fail. Error: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $e->getMessage()
                    )
                );

                $this->logger->debug(
                    __(
                        '%1 => %2[%3] Exception stack trace: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $e->getTraceAsString()
                    )
                );

                return 0;
            }
        } else {
            $this->logger->warning(
                __(
                    '%1 => %2[%3] Order #%4[%5] can not be shipped (canShip = false)',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $order->getIncrementId(),
                    $order->getEntityId()
                )
            );
        }

        return 0; // Return 0 if no shipment was created
    }

    /**
     * @param array $item
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return false|ShippingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function checkProductShipmentsItem($item, $orderStatusData, $order)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(ShippingInterface::PRODUCT_NO, $item[self::ARR_KEY_PRODUCTNO])
            ->addFilter(ShippingInterface::PICKLIST_NO, $item[self::ARR_KEY_PICKLISTNO])
            ->create();
        if (empty($this->ipiccoloShipmentRepository->getList($searchCriteria)->getItems())) {
            $this->logger->info(
                __(
                    '%1 => %2[%3] shipping for PicklistNo %4 ProductNo %5 not exists',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    $item[self::ARR_KEY_PICKLISTNO],
                    $item[self::ARR_KEY_PRODUCTNO]
                )
            );
            return true;
        }
        $this->logger->warning(
            __(
                '%1 => %2[%3] shipping for PicklistNo %4 ProductNo %5 already exists',
                __CLASS__,
                __FUNCTION__,
                __LINE__,
                $item[self::ARR_KEY_PICKLISTNO],
                $item[self::ARR_KEY_PRODUCTNO]
            )
        );
        return false;
    }

    /**
     * @param array $item
     * @param array $orderStatusData
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param int $shipmentId
     * @return false|ShippingInterface
     */
    private function ipiccoloShipmentSave($item, $orderStatusData, $order, $shipmentId = 0)
    {
        $ipiccoloShipment = $this->ipiccoloShipmentFactory->create();
        $ipiccoloShipment->setPicklistNo($item[self::ARR_KEY_PICKLISTNO]);
        $ipiccoloShipment->setProductNo($item[self::ARR_KEY_PRODUCTNO]);
        $ipiccoloShipment->setDeliveryGroup($item[self::ARR_KEY_DELIVERYGROUP]);
        $ipiccoloShipment->setQuantity($item[self::ARR_KEY_QUANTITY]);
        if ($shipmentId > 0) {
            $ipiccoloShipment->setSalesShipmentId($shipmentId);
        }
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $shipmentIdArr = [];
            $scanningTimestampArr = [];
            $shipmentMethodArr = [];
            foreach ($orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS] as $consignments) {
                if ($consignments[self::ARR_KEY_PICKLISTNO] == $item[self::ARR_KEY_PICKLISTNO]) {
                    $shipmentIdArr[] = $consignments[self::ARR_KEY_SHIPMENTID];
                    $scanningTimestampArr[] = $consignments[self::ARR_KEY_SCANNINGTIMESTAMP];
                    $shipmentMethodArr[] = $consignments[self::ARR_KEY_SHIPMENTMETHOD];
                }
            }
            $shipmentId = implode(',', array_unique($shipmentIdArr));
            $scanningTimestamp = implode(',', array_unique($scanningTimestampArr));
            $shipmentMethod = implode(',', array_unique($shipmentMethodArr));
            $ipiccoloShipment->setShipmentId($shipmentId);
            $ipiccoloShipment->setScanningTimestamp($scanningTimestamp);
            $ipiccoloShipment->setShipmentMethod($shipmentMethod);
            $ipiccoloShipment->setOrderId($order->getEntityId());
            $ipiccoloShipment->setIncrementId($order->getIncrementId());
            try {
                return $this->ipiccoloShipmentRepository->save($ipiccoloShipment);
            } catch (\Exception $e) {
                $this->logger->error(
                    __(
                        '%1 => %2[%3] ipiccolo shipment save fail. Error: %4',
                        __CLASS__,
                        __FUNCTION__,
                        __LINE__,
                        $e->getMessage()
                    )
                );
                return false;
            }
        } else {
            $this->logger->warning(
                __(
                    '%1 => %2[%3] Array key %4 not exists',
                    __CLASS__,
                    __FUNCTION__,
                    __LINE__,
                    self::ARR_KEY_CONSIGNMENTS
                )
            );
            return false;
        }
    }

    /**
     * @param string $shipmentmethod
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCarrierCode($order, $shipmentmethod = '')
    {
        if (!empty($shipmentmethod)) {
            return ($this->mapping($shipmentmethod)) ? $this->mapping($shipmentmethod) : 'custom';
        }
        return !empty($order->getData('shipping_method')) ? $order->getData('shipping_method') : 'custom';
    }

    /**
     * @param array $orderStatusData
     * @return array
     */
    private function getDeliveryGrouped(array $orderStatusData)
    {
        $deliveryGrouped = [];
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_PRODUCTSHIPMENTS)) {
            $productShipments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_PRODUCTSHIPMENTS];
            foreach ($productShipments as $item) {
                $deliveryGrouped[$item[self::ARR_KEY_PICKLISTNO]][self::ARR_KEY_PRODUCTSHIPMENTS][] = $item;
            }

        }
        if ($this->common->arrayKeyExistsMulti($orderStatusData, self::ARR_KEY_CONSIGNMENTS)) {
            $consignments = $orderStatusData[self::ARR_KEY_ORDERSTATUSDATA][self::ARR_KEY_CONSIGNMENTS];
            foreach ($consignments as $item) {
                $deliveryGrouped[$item[self::ARR_KEY_PICKLISTNO]][self::ARR_KEY_CONSIGNMENTS][] = $item;
            }
        }
        return $deliveryGrouped;
    }

    /**
     * @param $type
     * @param $source
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function mapping($source)
    {
        $type = 'ShipmentMethod';
        $destination = [];
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('type', $type)
            ->addFilter('source', $source)->create();
        $items = $this->mappingRepository->getList($searchCriteria)->getItems();
        foreach ($items as $item) {
            $destination[] = $item->getDestination();
        }
        return implode(',', $destination);
    }
}
