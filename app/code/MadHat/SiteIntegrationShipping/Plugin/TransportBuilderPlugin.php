<?php
namespace MadHat\SiteIntegrationShipping\Plugin;

use MadHat\SiteIntegrationBillings\Api\BillingsRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use MadHat\SiteIntegrationBillings\Model\Api\Billings as ApiBillings;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Psr\Log\LoggerInterface;
use Zend\Mime\Mime;
use Zend\Mime\Part as MimePart;
use Zend\Mime\Message as MimeMessage;

class TransportBuilderPlugin
{
    /**
     * @var DirectoryList
     */
    protected $directoryList;

    /**
     * @var LoggerInterface
     */
    protected LoggerInterface $logger;
    protected string|null $templateIdentifier = null;
    protected $templateVars = null;
    private ApiBillings $apiBillings;
    private OrderRepositoryInterface $orderRepository;
    private BillingsRepositoryInterface $billingsRepository;

    public function __construct(
        BillingsRepositoryInterface $billingsRepository,
        OrderRepositoryInterface $orderRepository,
        ApiBillings $apiBillings,
        DirectoryList $directoryList,
        LoggerInterface $logger
    ) {
        $this->billingsRepository = $billingsRepository;
        $this->orderRepository = $orderRepository;
        $this->apiBillings = $apiBillings;
        $this->directoryList = $directoryList;
        $this->logger = $logger;
    }

    /**
     * After plugin for getTransport() to attach PDF only to shipment emails.
     *
     * @param TransportBuilder $subject
     * @param mixed $result
     * @return mixed
     * @throws FileSystemException
     */
    public function afterGetTransport(TransportBuilder $subject, $result)
    {
        // Check if the template identifier equals the literal 'sales_email_shipment_template'
        if (!in_array($this->templateIdentifier,
            [
                'sales_email_shipment_guest_template',
                'sales_email_shipment_template'
            ]
        )) {
            $this->logger->debug('Attachment skipped: templateIdentifier does not match shipment email.');
            return $result;
        }

        // Create a ReflectionClass instance for the subject
        $reflection = new \ReflectionClass($subject);

        $this->logger->debug('Processing shipment email attachment.');

        // Build file path using Magento var directory.
        $filePaths = $this->getPdfFilePaths();
        foreach ($filePaths as $filePath) {
            if (file_exists($filePath)) {
                $fileContent = file_get_contents($filePath);
                // Create a MIME part for the PDF file.
                $attachmentPart = new MimePart($fileContent);
                $attachmentPart->type = 'application/pdf';
                $attachmentPart->disposition = Mime::DISPOSITION_ATTACHMENT;
                $attachmentPart->encoding = Mime::ENCODING_BASE64;
                $attachmentPart->filename = $this->getPdfFileName();

                // Retrieve the underlying message from the transport object.
                $message = $result->getMessage();
                $body = $message->getBody();
                if ($body instanceof MimeMessage) {
                    // Append the attachment part to the existing parts.
                    $parts = $body->getParts();
                    $parts[] = $attachmentPart;
                    $newBody = new MimeMessage();
                    $newBody->setParts($parts);
                    $message->setBody($newBody);
                }
            }
        }
        return $result;
    }

    /**
     * @param TransportBuilder $subject
     * @param $result
     * @return mixed
     */
    public function afterSetTemplateVars(TransportBuilder $subject, $result)
    {
        // Create a ReflectionClass instance for the subject
        $reflection = new \ReflectionClass($subject);

        // Retrieve the template identifier (stored in the protected templateIdentifier property)
        if ($reflection->hasProperty('templateIdentifier')) {
            $prop = $reflection->getProperty('templateIdentifier');
//            $prop->setAccessible(true);
            $this->templateIdentifier = $prop->getValue($subject);
        }
        if ($reflection->hasProperty('templateVars')) {
            $prop = $reflection->getProperty('templateVars');
            $this->templateVars = $prop->getValue($subject);
        }
        $this->logger->debug('Template identifier: ' . ($this->templateIdentifier ?? 'null'));

        return $result;
    }

    /**
     * @return array
     * @throws FileSystemException
     *
     * $this->templateVars
     * [
     * "order" => [], // Order object or array representation of order details
     * "order_id" => "1000000251", // Unique order identifier (string)
     * "shipment" => [], // Shipment object or array representation of shipment details
     * "shipment_id" => "3", // Unique shipment identifier (string)
     * "comment" => "", // Order or shipment comment (string)
     * "billing" => [], // Billing address details (array)
     * "payment_html" => "<dl class=\"payment-method adyen_cc\">\n    <dt class=\"title\">\n        Cards:     </dt>\n\n    </dl>\n", // HTML representation of the payment method used
     * "store" => [], // Store-related data (array)
     * "formattedShippingAddress" => "Manish TestJE<br />\n\nStreet Address #1<br />\nLe Mont Felard<br />\n\n\nJersey,  Jersey, JE3 1JA<br />\nJersey<br />\nT: <a href=\"tel:741258963\">741258963</a>\n\n", // Formatted shipping address as an HTML string
     * "formattedBillingAddress" => "Manish TestJE<br />\n\nStreet Address #1<br />\nLe Mont Felard<br />\n\n\nJersey,  Jersey, JE3 1JA<br />\nJersey<br />\nT: <a href=\"tel:741258963\">741258963</a>\n\n", // Formatted billing address as an HTML string
     * "order_data" => [ // Array with additional order details
     *      "customer_name" => "Manish TestJE", // Customer's full name (string)
     *      "is_not_virtual" => true, // Boolean indicating if the order contains physical products
     *      "email_customer_note" => "", // Email note for customer (string)
     *      "frontend_status_label" => "Processing" // Status label as seen on the frontend (string)
     *      ]
     * ]
 */
    private function getPdfFilePaths()
    {
        $fullFilePaths = [];
        try {
            $orderId = $this->templateVars['order_id'];
            $this->logger->debug('Order ID: ' . $orderId);
            if(!empty($orderId)) {
                $order = $this->orderRepository->get($orderId);
                $billings = $this->billingsRepository->getByOrderId($orderId);
                if(empty($billings)) {
                    $this->logger->debug(__('%1 %2 Billing not exists for order ID:',__LINE__,__CLASS__, $orderId));
                    return $fullFilePaths;
                }
                $path = $this->apiBillings->getPathInvoice($order);
                foreach ($billings as $billing) {
                    $fileName = $billing->getInvoiceFile();
                    $this->logger->debug('Invoice file name: ' . $fileName);
                    if (file_exists($path . $fileName)) {
                        $fullFilePaths[] = $path . $fileName;
                    }
                }
            }
            $this->logger->debug('PDF file paths: ' . implode(', ', $fullFilePaths));
        } catch (\Exception $e) {
            $this->logger->debug(__('%1 %2 Exception on get PDF File path. ERROR: %3',__LINE__,__CLASS__, $e->getMessage()));
        }
        return $fullFilePaths;
    }

    /**
     * @return string
     */
    private function getPdfFileName(): string
    {
        $orderId = $this->templateVars['order_id'];
        $fileName = 'invoice_receipt.pdf';
        if (!empty($orderId)) {
            try {
                $billings = $this->billingsRepository->getByOrderId($orderId);
                foreach ($billings as $billing) {
                    $fileNameArr[] = $billing->getInvoiceFile();
                }
                if(!empty($fileNameArr)) {
                    $fileName = implode('_', $fileNameArr);
                }
                return $fileName;
            } catch (\Exception $e) {
                $this->logger->error(__('%1, %2' , __CLASS__, $e->getMessage()));
                return $fileName;
            }
        }
        return $fileName;
    }
}
