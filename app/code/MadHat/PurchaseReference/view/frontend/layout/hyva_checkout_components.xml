<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="hyva.checkout.components">
            <referenceContainer name="checkout.section.additional-options">
                <block name="checkout.purchase-reference.title"
                       template="Hyva_Checkout::section/title.phtml"
                >
                    <action method="setTitle">
                        <argument name="title" translate="true" xsi:type="string">Options</argument>
                    </action>
                </block>

                <block name="checkout.purchase-reference"
                       template="MadHat_PurchaseReference::checkout/purchase-reference.phtml"
                       ifconfig="hyva_themes_checkout/component/purchase_reference/enable"
                >
                    <arguments>
                        <argument name="magewire" xsi:type="object">
                            \MadHat\PurchaseReference\Magewire\Checkout\PurchaseReference
                        </argument>
                    </arguments>
                </block>
            </referenceContainer>
        </referenceBlock>
    </body>
</page>

