<div class="fieldset-wrapper"
     css="$data.additionalClasses"
     attr="'data-level': $data.level, 'data-index': index"
     data-bind="visible: $data.visible === undefined ? true: $data.visible">
    <div class="fieldset-wrapper-title"
         attr="tabindex: !collapsible ? -1 : 0,
               'data-state-collapsible': collapsible ? opened() ? 'open' : 'closed' : null"
         click="toggleOpened"
         keyboard="13: toggleOpened"
         if="label">

        <strong css="'admin__collapsible-title': collapsible,
                      title: !collapsible,
                      '_changed': changed,
                      '_loading': loading,
                      '_error': error">
            <span translate="label"/>
            <span class="admin__page-nav-item-messages" if="collapsible">
                <span class="admin__page-nav-item-message _changed">
                    <span class="admin__page-nav-item-message-icon"></span>
                    <span class="admin__page-nav-item-message-tooltip"
                          data-bind="i18n: 'Changes have been made to this section that have not been saved.'">
                    </span>
                </span>
                <span class="admin__page-nav-item-message _error">
                    <span class="admin__page-nav-item-message-icon"></span>
                    <span class="admin__page-nav-item-message-tooltip"
                          data-bind="i18n: 'This tab contains invalid data. Please resolve this before saving.'">
                    </span>
                </span>
                <span class="admin__page-nav-item-message-loader">
                    <span class="spinner">
                       <span repeat="8"/>
                    </span>
               </span>
            </span>
        </strong>
    </div>

    <div class="admin__fieldset-wrapper-content"
         css="'admin__collapsible-content': collapsible, '_show': opened, '_hide': !opened()">
        <fieldset if="opened() || _wasOpened || initializeFieldsetDataByDefault"
                class="admin__fieldset">
          <div class="uibuilder-modal-design-heading-wrapper">
            <label class="uibuilder-modal-design-heading" data-bind="i18n: 'CSS box'"></label>
            <each args="getRegion('simply')">
              <div class="uibuilder-design-simply">
                  <!--ko template: getTemplate()--><!-- /ko -->
                  <label attr="for: uid" data-bind="i18n: 'Simplify controls'"></label>
                </div>
            </each>
          </div>
          <div class="uibuilder-modal-design-content">
          <div class="uibuilder-modal-design-left">
            <div class="uibuilder-modal-design-margin">
              <label class="uibuilder-modal-design-element-heading" data-bind="i18n: 'margin'"></label>
              <div each="getRegion('margin')" render=""/>
              <div class="uibuilder-modal-design-margin-unit" each="getRegion('margin-unit')" render=""/>
            <div class="uibuilder-modal-design-border">
              <label class="uibuilder-modal-design-element-heading" data-bind="i18n: 'border'"></label>
              <div each="getRegion('border')" render=""/>
              <div class="uibuilder-modal-design-border-unit" each="getRegion('border-unit')" render=""/>
              <div class="uibuilder-modal-design-padding">
                <label class="uibuilder-modal-design-element-heading" data-bind="i18n: 'padding'"></label>
                <div each="getRegion('padding')" render=""/>
                <div class="uibuilder-modal-design-padding-unit" each="getRegion('padding-unit')" render=""/>
              </div>
            </div>
          </div>
          <div class="uibuilder-modal-design-radius">
            <div each="getRegion('borderRadius')" render=""/>
            <div class="uibuilder-modal-design-border-radius-unit" each="getRegion('borderRadius-unit')" render=""/>
          </div>
        </div>
        <div class="uibuilder-modal-design-right" each="getRegion('right')" render=""/>
      </div>
      <div class="uibuilder-modal-design-footer" each="getRegion('footer')" render=""/>
      </fieldset>
    </div>
</div>