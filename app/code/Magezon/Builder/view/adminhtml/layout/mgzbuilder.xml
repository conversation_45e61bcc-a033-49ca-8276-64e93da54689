<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_Builder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<head>
		<css src="Magezon_Core::css/owlcarousel/owl.carousel.min.css"/>
		<css src="Magezon_Core::css/animate.css"/>
		<css src="Magezon_Core::css/fontawesome5.css"/>
		<css src="Magezon_Core::css/mgz_font.css"/>
		<css src="Magezon_Core::css/mgz_bootstrap.css"/>
		<css src="Magezon_Builder::vendor/codemirror/lib/codemirror.css"/>
		<css src="Magezon_Builder::css/bootstrap.css"/>
		<css src="Magezon_Builder::css/openiconic.min.css"/>
		<css src="Magezon_Builder::css/builder.css" media="all"/>
		<css src="Magezon_Builder::css/styles.css" media="all"/>
		<remove src="Magento_Backend::js/bootstrap/editor.js"/>
	</head>
	<body>
		<referenceBlock name="algolia_common" remove="true"/>
		<referenceBlock name="algolia_tracking" remove="true"/>
		<referenceBlock name="algolia.tracking" remove="true"/>
		<referenceBlock name="before.body.end">
			<block class="Magento\Backend\Block\Template" name="builder_head_editor" template="Magezon_Builder::editor.phtml" />
		</referenceBlock>
	</body>
</page>