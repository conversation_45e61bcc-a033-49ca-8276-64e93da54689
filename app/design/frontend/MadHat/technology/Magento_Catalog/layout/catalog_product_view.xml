<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="page.main.title">
            <arguments>
                <argument name="css_class" xsi:type="string">mt-2 product text-3xl md:text-4xl font-semibold text-left
                    px-0 md:mb-3
                </argument>
                <argument name="add_base_attribute" xsi:type="string">itemprop="name"</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="description">
            <arguments>
                <argument name="title" xsi:type="string" translate="true">Description</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="product.detail.page">
            <block class="Magento\Catalog\Block\Product\View\Details"
                   name="product.supply.and.deal.section"
                   template="Magento_Catalog::product/view/sections/supply-and-deal-sections.phtml">
                <block name="product.section.title.renderer.new"
                       template="Magento_Catalog::product/view/sections/default-section-title.phtml"/>

                <block name="related_custom" group="supply_and_deal" class="Magento\Catalog\Block\Product\View"
                       template="Magento_Catalog::product/slider/product-slider.phtml">
                    <arguments>
                        <argument name="type" xsi:type="string">related</argument>
                        <argument name="title" xsi:type="string" translate="true">Supplies</argument>
                        <argument name="heading_tag" xsi:type="string">h2</argument>
                    </arguments>
                </block>

                <block name="upsell_custom" group="supply_and_deal" class="Magento\Catalog\Block\Product\View"
                       template="Magento_Catalog::product/slider/product-slider.phtml">
                    <arguments>
                        <argument name="type" xsi:type="string">upsell</argument>
                        <argument name="title" xsi:type="string" translate="true">Deals!</argument>
                        <argument name="heading_tag" xsi:type="string">h2</argument>
                    </arguments>
                </block>

            </block>
        </referenceBlock>

        <move element="product.info.review" destination="product.detail.page" />
    </body>
</page>
