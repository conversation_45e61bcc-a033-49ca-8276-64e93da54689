<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

// phpcs:disable Magento2.Files.LineLength.MaxExceeded

use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Checkout\Block\Cart\Item\Renderer;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;

/** @var Renderer $block */
/** @var Escaper $escaper */

$item = $block->getItem();
$product = $item->getProduct();
$isVisibleProduct = $product->isVisibleInSiteVisibility();

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);
$childProduct = null;
if ($product->getTypeId() === Configurable::TYPE_CODE) {
    $childProduct = $block->getChildProduct();
    $regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $childProduct);
    $finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $childProduct);
} else {
    $regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
    $finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
}
/** @var HeroiconsOutline $heroIcons */
$heroIcons = $viewModels->require(HeroiconsOutline::class);

/** @var \Hyva\Theme\ViewModel\SvgIcons $hyvaicons */
$hyvaicons = $viewModels->require(\Hyva\Theme\ViewModel\SvgIcons::class);
?>
<tbody class="cart item bg-white">
<tr class="item-info align-top text-left lg:text-right flex flex-wrap lg:table-row">
    <td data-th="<?= $escaper->escapeHtml(__('Item')) ?>" class="col item col-span-5 pt-2 mb-5 pr-2 flex gap-4 text-left w-full lg:w-auto">
        <div class="product-item-photo-wrapper flex flex-col md:flex-row gap-4 relative mb-4">
            <div class="flex items-center gap-2 max-h-16">
                <?= /* @noEscape */ $block->getActions($item) ?>
            </div>
            <?php if ($childProduct) : ?>
                <?php $childProductUrl = $childProduct->getUrlModel()->getUrl($childProduct); ?>
            <?php endif; ?>
            <?php if ($block->hasProductUrl()): ?>
            <a href="<?= ($childProduct) ? $childProductUrl : $escaper->escapeUrl($block->getProductUrl()) ?>"
               title="<?= $escaper->escapeHtmlAttr($block->getProductName()) ?>"
               tabindex="-1"
               class="product-item-photo width-full max-w-16 shrink-0">
                <?php else: ?>
                <span class="product-item-photo shrink-0">
                <?php endif;?>
                <?= $block->getImage($block->getProductForThumbnail(), 'cart_page_product_thumbnail')
                    ->setTemplate('Magento_Catalog::product/image.phtml')
                    ->toHtml() ?>
                <?php if ($block->hasProductUrl()): ?>
            </a>
        <?php else: ?>
            </span>
        <?php endif; ?>
        </div>
        <div class="product-item-details grow">
            <strong class="product-item-name">
                <?php if ($block->hasProductUrl()): ?>
                    <a class="hover:text-cblue truncate font-semibold max-w-64 md:max-w-xl block" href="<?= ($childProduct) ? $childProductUrl : $escaper->escapeUrl($block->getProductUrl()) ?>" title="<?= $escaper->escapeHtml($block->getProductName()) ?>"><?= $escaper->escapeHtml($block->getProductName()) ?></a>
                <?php else: ?>
                    <?= $escaper->escapeHtml($block->getProductName()) ?>
                <?php endif; ?>
            </strong>
            <p class="product-item-article mb-0">
                <span class=""><?= __('SKU') ?></span>
                <span class=""><?= $product->getSku() ?></span>
            </p>
            <?php if ($options = $block->getOptionList()): ?>
                <div class="product-item-attributes">
                    <?php foreach ($options as $option): ?>
                        <?php $formatedOptionValue = $block->getFormatedOptionValue($option) ?>
                        <span class="break-words inline-block">
                            <?php if (isset($formatedOptionValue['full_view'])): ?>
                                <?= $escaper->escapeHtml($formatedOptionValue['full_view']) ?>
                            <?php else: ?>
                                <?= $escaper->escapeHtml($formatedOptionValue['value'], ['span', 'a']) ?>
                            <?php endif; ?>
                        </span>
                    <?php endforeach; ?>
                </div>
            <?php endif;?>
            <?php if ($messages = $block->getMessages()): ?>
                <?php foreach ($messages as $message): ?>
                    <div class= "cart item message <?= $escaper->escapeHtmlAttr($message['type']) ?>">
                        <div><?= $escaper->escapeHtml($message['text']) ?></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
            <?php if ($addInfoBlock): ?>
                <?= $addInfoBlock->setItem($item)->toHtml() ?>
            <?php endif;?>

            <div class="col price">
                    <span class="hidden font-bold mt-2">
                        <?= $escaper->escapeHtml(__('Price')) ?>
                    </span>
                <?php if ($finalPrice < $regularPrice) : ?>
                    <span class="cart-price-value after-discount"><?= $block->getUnitPriceHtml($item) ?></span>
                    <div class="old-price-excl-tax">
                            <span class="font-regular line-through text-xs text-gray-900">
                                <?= $block->escapeHtml(
                                    $this->helper(Magento\Checkout\Helper\Data::class)->formatPrice($regularPrice),
                                    ['span']
                                ) ?>
                            </span>
                    </div>
                <?php else: ?>
                    <span class="cart-price-value"><?= $block->getUnitPriceHtml($item) ?></span>
                <?php endif; ?>
            </div>
            <div class="col qty flex items-center">
                <span class="hidden font-bold">
                    <?= $escaper->escapeHtml(__('Qty')) ?>
                </span>
                <div x-data="initCartQtyField_<?= $escaper->escapeHtmlAttr($item->getId()) ?>" class="field qty">
                    <div class="control qty addtocart-stepper flex aligns-center">
                        <button
                            type="button"
                            @click="itemQty--"
                            @click.debounce.1000ms="updateItemQty($event);"
                            class="btn btn-primary flex justify-center items-center p-0 size-8 shadow-none text-base font-semibold"
                        >
                            <?= $heroIcons->minusHtml('', 20, 20, ['aria-hidden' => 'true']); ?>
                        </button>
                        <input id="cart-<?= $escaper->escapeHtmlAttr($item->getId()) ?>-qty"
                               name="cart[<?= $escaper->escapeHtmlAttr($item->getId()) ?>][qty]"
                               value="<?= $escaper->escapeHtmlAttr($block->getQty()) ?>"
                               type="number"
                               size="4"
                               step="any"
                               title="<?= $escaper->escapeHtmlAttr(__('Qty')) ?>"
                               @change.debounce.1000ms="updateItemQty($event)"
                               class="qty form-input p-1 !px-0 size-8 !min-h-8 min-w-20 mx-1 text-xl font-semibold text-center focus:ring-0"
                               required="required"
                               min="0"
                               :value="itemQty"
                               x-model.number="itemQty"
                               data-role="cart-item-qty"/>
                        <button
                            type="button"
                            @click="itemQty++"
                            @click.debounce.1000ms="updateItemQty($event);"
                            class="btn btn-primary flex justify-center items-center p-0 size-8 shadow-none text-base font-semibold"
                        >
                            <?= $heroIcons->plusHtml('', 20, 20, ['aria-hidden' => 'true']); ?>
                        </button>
                    </div>
                </div>

                <div class="subtotal">
                    <span class="hidden font-bold">
                        <?= $escaper->escapeHtml(__('Subtotal')) ?>
                    </span>
                    <?php if ($finalPrice < $regularPrice) : ?>
                        <span class="after-discount"><?= $block->getRowTotalHtml($item) ?></span>
                    <?php else: ?>
                        <span class=""><?= $block->getRowTotalHtml($item) ?></span>
                    <?php endif; ?>
                </div>

            </div>
        </div>
    </td>

</tr>
</tbody>

<script>
    function initCartQtyField_<?= $escaper->escapeHtmlAttr($item->getId()) ?>()
    {
        return {
            itemId: <?= $item->getId() ?>,
            itemQty: <?= $block->getQty() ?>,
            updateItemQty() {
                let qtyInput = this.$root.querySelector('.qty.form-input');
                let updateCartAction = document.getElementsByName('update_cart_action')[0];
                const loader = this.createAjaxLoader();
                this.addAjaxLoader(loader);
                updateCartAction.click();
            },
            createAjaxLoader() {
                let div = this.$root.querySelector('div.addtocart-stepper');
                const loader = document.createElement('div');
                loader.className = 'absolute flex justify-center items-center loader';
                <?php /* loader.innerHTML = `<?= $hyvaicons->renderHtml(
                    'loader',
                    'w-full h-full p-2',
                    24, 24,
                    ['title' => $escaper->escapeHtml(__('Loading...'))]
                ); ?>`; */?>
                return loader;
            },
            addAjaxLoader(loader) {
                let div = this.$root.querySelector('div.addtocart-stepper');
                div.prepend(loader);
                div.classList.add('relative', '[&>:not(.loader)]:invisible');
            }
        };
    }
</script>
