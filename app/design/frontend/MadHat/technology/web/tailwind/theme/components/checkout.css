.checkout-default,
.hyva_checkout-index-index {
  #header {
    .header-left {
      @apply m-0;
    }
  }

  #hyva-checkout-main {
    .nav-breadcrumbs {
      .breadcrumbs {
        @apply flex-wrap;
      }
    }
  }

  #shipping-method-option-amstrates1,
  #shipping-method-option-amstrates2,
  #shipping-method-option-amstrates3,
  #shipping-method-option-amstrates4,
  #shipping-method-option-amstrates5,
  #shipping-method-option-amstrates6 {
    label {
      &:first-child {
        @apply relative pl-[72px];
        &::before {
          @apply absolute left-[-4px] top-0 bottom-0 my-auto w-[57px] h-[40px];
          content: "";
        }
      }
    }
  }

  #shipping-method-option-amstrates1 {
    label {
      &:first-child {
        @apply relative;
        &::before {
          background: url('../svg/payment-icons/shipping-method/royal-mail.svg') center center no-repeat;
          background-size:contain;
        }
      }
    }
  } 
  
  #shipping-method-option-amstrates2,
  #shipping-method-option-amstrates3,
  #shipping-method-option-amstrates4 {
    label {
      &:first-child {
        &::before {
          background: url('../svg/payment-icons/shipping-method/dpd.svg') center center no-repeat;
          background-size:100%;
        }
      }
    }
  } 
  
  #shipping-method-option-amstrates5 {
    label {
      &:first-child {
        &::before {
          background: url('../svg/payment-icons/shipping-method/fedex.svg') center center no-repeat;
          background-size:100%;
        }
      }
    }
  } 

  #shipping-method-option-amstrates6 {
    label {
      &:first-child {
        &::before {
          background: url('../svg/payment-icons/shipping-method/ups.svg') center center no-repeat;
          background-size:100%;
        }
      }
    }
  }     

  #hyva-checkout-container {
    .cards-container { @apply w-[100px];}
    .cards-container svg { @apply w-full; }

    #payment-method-list {
      svg {
        @apply pointer-events-none;
        
        title {
          @apply hidden;
        }
      }

      #payment-method-option-adyen_googlepay {
        .flex.gap-2.items-center {
          span:first-of-type {
            @apply bg-white rounded-sm;

            svg {
              @apply w-11 h-7;
            }
          }
        }

        #gpay-button-online-api-id {
          @apply !min-w-full;
        }
      }

      #payment-method-option-adyen_directdebit_GB {
        .flex.gap-2.items-center {
          span:first-of-type {
            svg {
              @apply w-20;
            }
          }
        }
      }

      #payment-method-option-adyen_applepay {
        .adyen-checkout__applepay__button {
          @apply !w-full
        }
      }
      
    }
    
    #agreement_1-label {
      @apply text-2xl text-center border-b border-cgrey-65 pb-3 mb-7 uppercase;
    }

    .close-icon {
      @apply absolute top-4 md:top-6 right-5 md:right-6  text-2xl md:text-3xl;
    }

    .dialog-height {
      max-height: calc(100% - 40px);
    }

    .agreement-modal-content {
      @apply text-left;
      p {
        @apply mb-5;
      }
    }
    .btn-secondary {
      @apply bg-transparent border-cgrey-65;
    }

    #quote-actions {
      .terms-conditions-label {
        font-size: 14px;
        @apply font-semibold;

        a {
          @apply underline underline-offset-4;

          &:hover {
            @apply text-cgrey-90 font-medium;
          }
        }
      }
    }

    .field-street {
      #shipping-street-0 {
        @apply mb-6;
      }
    }

    .field-wrapper,
    .coupon-code {
      .form-input:focus {
        @apply border-2 border-primary-darker;
        box-shadow: none;
      }
    }

    .coupon-code {
      .form-input+label.label {
        @apply bg-cgrey-0 absolute left-1 font-semibold text-xs tracking-wide text-cgrey-65  transition duration-200 ease-in-out px-2;
      }
    }

    .coupon-code:focus-within label.label,
    .coupon-code input:not(:placeholder-shown)+label.label  {
      transform: translateY(-1.2rem) scale(0.9);
      @apply bg-cgrey-0 leading-4 left-1 text-cgrey-90 tracking-wide;
    }

    .field-wrapper .items-center.gap-4 {
      .form-input+label.label {
        @apply absolute top-4 left-1 font-semibold text-xs tracking-wide text-cgrey-65  transition duration-200 ease-in-out px-2;
      }
      .form-input:required {
        + label {
          &::after {
            @apply absolute content-['*'] text-cred text-base/4 font-normal;
          }
        }
      }
    }

    .field-wrapper {
      .items-center.gap-4.select-out {
        @apply relative;

        .form-input+label.label {
          transform: translateY(-1.2rem) scale(0.9);
          @apply leading-4 left-1 text-cgrey-90 tracking-wide bg-cgrey-50;
        }
      }
    }

    .field-wrapper .items-center.gap-4:focus-within label.label,
    .field-wrapper .items-center.gap-4 input:not(:placeholder-shown)+label.label {
      transform: translateY(-1.2rem) scale(0.9);
      @apply bg-cgrey-50 leading-4 left-1 text-cgrey-90 tracking-wide;
    }

    .field-wrapper.field-error {
      .form-input {
        @apply border-cred;
      }
    }

    .form-input.invalid {
      @apply border-cred;
    }

    .column.column-main {
      @apply bg-cgrey-50 p-6;

      .nav-breadcrumbs {
        @apply mb-4 pb-6 border-cgrey-65;
      }

      .nav-main {
        @apply bg-transparent;

        .btn {
          @apply py-4 px-6;
        }

        .btn-next {
          margin-left: auto;
        }

        .justify-end {
          @apply justify-between;

          .btn-primary {
            @apply bg-primary justify-center;
          }

          .btn-secondary {
            @apply bg-transparent border-cgrey-65 justify-center;
          }
        }
      }
    }

    #billing-details {
      address {
        @apply not-italic;
      }
    }

    #quote-summary {

      address {
        @apply not-italic;
      }

      .price-summary {
        @apply bg-white p-6 border-[12px] border-cgrey-50;

        .section-title {
          @apply mb-4;
        }

        .total-segments {
          @apply mt-9 mb-5;
        }

        .cart-items {
          .product-title {
            @apply line-clamp-none;

            .text-sm {
              @apply text-xs;
            }
          }

          .product-price {
            @apply text-black;
          }

          .leading-relaxed {
            @apply hidden;
          }

          .flex-none {
            span.ring-4 {
              top: 0;
            }
          }
        }
      }

      .grand_total {
        @apply mt-5;

        .label {
          @apply text-base;
        }
      }
    }

    h2 {
      @apply text-base font-bold;
    }
  }
}


