<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <head>
        <remove src="Magezon_Core::css/styles.css"/>
        <remove src="Magezon_Core::css/owlcarousel/owl.carousel.min.css"/>
        <remove src="Magezon_Core::css/animate.css"/>
        <remove src="Magezon_Core::css/mgz_font.css"/>
        <remove src="Magezon_Builder::css/openiconic.min.css"/>
        <remove src="Magezon_Builder::css/styles.css"/>
        <remove src="Magezon_Builder::css/common.css"/>
    </head>
    <body>
        <referenceContainer name="head.additional">
            <referenceBlock name="font-awesome" remove="true"/>
        </referenceContainer>
        <referenceContainer name="header.container">
            <block name="header_top_section"
                template="Magento_Theme::html/header-top-section.phtml"
                before="header-content"
            />
            <block name="header_notice"
                   template="Magento_Theme::html/header/header-notice.phtml"
                   before="header_top_section"
            />
        </referenceContainer>

        <referenceBlock name="header-content">
            <block name="customer_type" template="Magento_Theme::html/header/customer_type.phtml"/>
        </referenceBlock>

        <referenceBlock name="header-notification" remove="true"/>
        <referenceBlock name="header-usps" remove="true"/>

        <move element="topmenu_mobile" destination="header-content" after="-"/>
        <move element="topmenu_desktop" destination="header-content" after="-"/>

        <referenceContainer name="footer" htmlClass="footer footer-content">
            <block name="footer-content" template="Magento_Theme::html/footer.phtml">
                <block class="Magento\Cms\Block\Block" name="footer-cms-content">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_4_columns</argument>
                    </arguments>
                </block>
               <block class="Magento\Cms\Block\Block" name="footer-cms-copyright">
                    <arguments>
                        <argument name="block_id" xsi:type="string">footer_bottom</argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>

        <referenceBlock name="script-alpine-js">
            <block name="alpine-plugin-ajax-cart" template="Magento_Theme::page/js/plugins/ajax-cart.phtml">
                <arguments>
                    <argument name="delay" xsi:type="number">500</argument> <!-- Extra delay in milliseconds added to the loader (true/false) -->
                    <argument name="show_sku" xsi:type="boolean">false</argument> <!-- Show Sku (true/false) -->
                    <argument name="display_on_success" xsi:type="string">false</argument> <!-- What to show on success (false/minicart/modal) -->
                </arguments>
            </block>
        </referenceBlock>

        <referenceBlock name="cart-drawer">
            <arguments>
                <argument name="show_sku" xsi:type="boolean">false</argument> <!-- Show sku in product options (true/false) -->
                <argument name="show_options" xsi:type="boolean">true</argument> <!-- Show product options (true/false) -->
                <argument name="qty_style" xsi:type="string">incrementor</argument> <!-- Qty style (text/input/select/incrementor) -->
            </arguments>
        </referenceBlock>
    </body>
</page>
