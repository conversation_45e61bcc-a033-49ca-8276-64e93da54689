<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Form\EntityFieldSelectInterface;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var EntityFieldSelectInterface $element */
/** @var Escaper $escaper */

$element = $block->getData('element');

// Enforce the input type attribute to align with the specified template.
$element->setAttribute('type', 'select');
?>
<div class="block font-medium text-gray-700 <?= $element->isRequired() ? 'required' : 'not-required' ?>">

    <?= /* @noEscape */ $element->getRenderer()->renderBefore($element) ?>

    <?php if ($element->hasRelatives()): ?>
        <div class="space-y-2">
    <?php endif ?>

    <div class="flex items-center gap-4 select-out">
        <select class="<?= $escaper->escapeHtmlAttr($element->renderClass(['block w-full form-input renderer-select'])) ?>"
                <?php if ($element->hasAttributes()): ?>
                    <?= /* @noEscape */ $element->renderAttributes($escaper) ?>
                <?php endif ?>
        >
            <?php foreach ($element->getOptions() as $key => $option): ?>
                <?php if (is_object($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option->getValue()) ?>">
                        <?= $escaper->escapeHtml(__($option->getLabel())) ?>
                    </option>
                <?php elseif (is_array($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($option['value']) ?>">
                        <?= $escaper->escapeHtml(__($option['label'])) ?>
                    </option>
                <?php elseif (is_string($option)): ?>
                    <option value="<?= $escaper->escapeHtmlAttr($key) ?>">
                        <?= $escaper->escapeHtml(__($option)) ?>
                    </option>
                <?php endif ?>
            <?php endforeach ?>
        </select>

        <?= /* @noEscape */ $element->getRenderer()->renderLabel($element) ?>

        <?php if ($element->hasTooltip()): ?>
            <?= /* @noEscape */ $element->getRenderer()->renderTooltip($element) ?>
        <?php endif ?>
    </div>

    <?php if ($element->hasRelatives()): ?>
        <?php foreach ($element->getRelatives() as $relative): ?>
            <?php if ($relative->isVisible()): ?>
                <?= /* @noEscape */ $relative->render() ?>
            <?php endif ?>
        <?php endforeach ?>
    <?php endif ?>

    <?php if ($element->hasRelatives()): ?>
        </div>
    <?php endif ?>

    <?= /* @noEscape */ $element->getRenderer()->renderAfter($element) ?>
</div>
